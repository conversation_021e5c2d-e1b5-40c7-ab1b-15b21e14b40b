package com.armamc;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import org.lwjgl.glfw.GLFW;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TerrenoCompraModClient implements ClientModInitializer {
    
    private static KeyBinding toggleModKey;
    private boolean modEnabled = false;
    private boolean autoClicking = false;
    private String currentProtectionNumber = null;
    private int clickDelay = 0;
    private static final int CLICK_INTERVAL = 20; // 1 segundo (20 ticks)
    
    // Padrões regex para detectar mensagens
    private static final Pattern PROTECTION_PATTERN = Pattern.compile("Proteção #(\\d+)");
    private static final Pattern BUY_PATTERN = Pattern.compile("\\[Comprar Proteção\\]");
    
    @Override
    public void onInitializeClient() {
        TerrenoCompraMod.LOGGER.info("Terreno Compra Mod Client inicializado!");
        
        // Registrar keybinding para ativar/desativar o mod
        toggleModKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.terreno-compra-mod.toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_P,
            "category.terreno-compra-mod"
        ));
        
        // Registrar eventos
        registerEvents();
    }
    
    private void registerEvents() {
        // Evento de tick do cliente
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // Verificar se a tecla foi pressionada
            if (toggleModKey.wasPressed()) {
                toggleMod();
            }
            
            // Auto-clique se estiver ativo
            if (autoClicking && modEnabled) {
                handleAutoClick(client);
            }
        });
        
        // Evento de recebimento de mensagens do chat
        ClientReceiveMessageEvents.GAME.register((message, overlay) -> {
            if (!modEnabled) return;
            
            String messageText = message.getString();
            TerrenoCompraMod.LOGGER.info("Mensagem recebida: {}", messageText);
            
            // Verificar se é uma mensagem de proteção
            Matcher protectionMatcher = PROTECTION_PATTERN.matcher(messageText);
            if (protectionMatcher.find()) {
                currentProtectionNumber = protectionMatcher.group(1);
                TerrenoCompraMod.LOGGER.info("Número da proteção capturado: {}", currentProtectionNumber);
                startAutoClicking();
                return;
            }
            
            // Verificar se apareceu a opção de compra
            if (BUY_PATTERN.matcher(messageText).find() && currentProtectionNumber != null) {
                TerrenoCompraMod.LOGGER.info("Opção de compra detectada! Executando comando...");
                stopAutoClicking();
                executeCompraCommand();
            }
        });
    }
    
    private void toggleMod() {
        modEnabled = !modEnabled;
        MinecraftClient client = MinecraftClient.getInstance();
        
        if (modEnabled) {
            if (client.player != null) {
                client.player.sendMessage(Text.literal("§a[Terreno Mod] Ativado! Clique com a vareta para começar."), false);
            }
            TerrenoCompraMod.LOGGER.info("Mod ativado!");
        } else {
            if (client.player != null) {
                client.player.sendMessage(Text.literal("§c[Terreno Mod] Desativado!"), false);
            }
            stopAutoClicking();
            currentProtectionNumber = null;
            TerrenoCompraMod.LOGGER.info("Mod desativado!");
        }
    }
    
    private void startAutoClicking() {
        if (!autoClicking) {
            autoClicking = true;
            clickDelay = 0;
            TerrenoCompraMod.LOGGER.info("Auto-clique iniciado");
            
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                client.player.sendMessage(Text.literal("§e[Terreno Mod] Auto-clique iniciado. Aguardando opção de compra..."), false);
            }
        }
    }
    
    private void stopAutoClicking() {
        if (autoClicking) {
            autoClicking = false;
            TerrenoCompraMod.LOGGER.info("Auto-clique parado");
        }
    }
    
    private void handleAutoClick(MinecraftClient client) {
        if (client.player == null || client.world == null) return;
        
        clickDelay--;
        if (clickDelay <= 0) {
            clickDelay = CLICK_INTERVAL;
            
            // Simular clique direito
            if (client.crosshairTarget != null && client.crosshairTarget.getType() == HitResult.Type.BLOCK) {
                BlockHitResult blockHit = (BlockHitResult) client.crosshairTarget;
                client.interactionManager.interactBlock(client.player, Hand.MAIN_HAND, blockHit);
                TerrenoCompraMod.LOGGER.debug("Auto-clique executado");
            }
        }
    }
    
    private void executeCompraCommand() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null && currentProtectionNumber != null) {
            String command = "/imob comprar " + currentProtectionNumber;
            TerrenoCompraMod.LOGGER.info("Executando comando: {}", command);
            
            client.player.networkHandler.sendChatCommand(command.substring(1)); // Remove a barra do comando
            client.player.sendMessage(Text.literal("§a[Terreno Mod] Comando executado: " + command), false);
            
            // Reset do estado
            currentProtectionNumber = null;
        }
    }
}
