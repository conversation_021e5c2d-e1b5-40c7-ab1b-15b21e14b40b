# Terreno Compra Mod

Mod para Minecraft Fabric 1.21.5 que automatiza a compra de terrenos no servidor armamc.com.

## Funcionalidades

- **Detecção automática de proteções**: Captura o número da proteção quando você clica com uma vareta
- **Monitoramento do chat**: Detecta quando aparece a mensagem "[Comprar Proteção]"
- **Auto-clique**: Continua clicando automaticamente até que a opção de compra apareça
- **Execução automática de comando**: Executa automaticamente `/imob comprar [numero]` quando detecta a opção

## Como usar

1. **Ativar o mod**: Pressione a tecla `P` para ativar/desativar o mod
2. **Clique com a vareta**: Quando o mod estiver ativo, clique com a vareta na proteção desejada
3. **Aguarde**: O mod irá:
   - Capturar o número da proteção (ex: "Proteção #700786")
   - Começar a clicar automaticamente com o botão direito
   - Monitorar o chat esperando a mensagem "[Comprar Proteção]"
   - Executar automaticamente o comando `/imob comprar 700786`

## Controles

- **Tecla P**: Ativa/desativa o mod
- **Mensagens no chat**: O mod informa quando está ativo, quando captura uma proteção e quando executa comandos

## Como funciona

1. O mod detecta quando você clica com uma vareta e monitora as mensagens do chat
2. Quando encontra uma mensagem com "Proteção #[numero]", captura o número
3. Inicia o auto-clique (clica a cada 1 segundo) para verificar se a proteção está disponível
4. Quando detecta "[Comprar Proteção]" no chat, para o auto-clique e executa o comando
5. O comando executado será `/imob comprar [numero]` onde [numero] é o número capturado

## Instalação

1. Certifique-se de ter o Fabric Loader instalado para Minecraft 1.21.5
2. Coloque o arquivo `.jar` do mod na pasta `mods` do seu Minecraft
3. Execute o jogo e use a tecla P para ativar o mod

## Requisitos

- Minecraft 1.21.5
- Fabric Loader 0.16.14+
- Fabric API 0.127.0+1.21.5

## Compilação

Para compilar o mod:

```bash
./gradlew build
```

O arquivo `.jar` será gerado na pasta `build/libs/`.

## Notas importantes

- O mod só funciona no lado cliente (client-side)
- Funciona especificamente com o formato de mensagens do servidor armamc.com
- O auto-clique para quando a opção de compra é detectada
- Todas as ações são registradas no log para debug
